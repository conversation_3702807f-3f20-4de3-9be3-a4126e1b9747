<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fonction Netlify</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .endpoint-test { display: flex; gap: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🧪 Test de la Fonction Netlify</h1>
    
    <div class="test-section info">
        <h2>🔍 Informations d'Environnement</h2>
        <p><strong>Hostname:</strong> <span id="hostname"></span></p>
        <p><strong>URL complète:</strong> <span id="full-url"></span></p>
        <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
    </div>

    <div class="test-section">
        <h2>🎯 Test des Endpoints</h2>
        <div class="endpoint-test">
            <button onclick="testEndpoint('/.netlify/functions/lead')">Test /.netlify/functions/lead</button>
            <button onclick="testEndpoint('/api/lead')">Test /api/lead (redirect)</button>
        </div>
        <div id="endpoint-results"></div>
    </div>

    <div class="test-section">
        <h2>📝 Test Complet du Formulaire</h2>
        <button onclick="testFormSubmission()">🚀 Tester Soumission Complète</button>
        <div id="form-results"></div>
    </div>

    <script>
        // Affichage des informations d'environnement
        document.getElementById('hostname').textContent = window.location.hostname;
        document.getElementById('full-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        async function testEndpoint(endpoint) {
            const resultsDiv = document.getElementById('endpoint-results');
            resultsDiv.innerHTML = `<p>⏳ Test de ${endpoint}...</p>`;

            const testData = {
                name: "Test User",
                company: "Test Company",
                email: "<EMAIL>",
                phone: "+33123456789",
                status: "optimisation",
                context: "Test de la fonction Netlify",
                hp: "", // honeypot
                ts: Date.now(),
                utm_source: "",
                utm_campaign: ""
            };

            try {
                console.log(`🔗 Testing endpoint: ${endpoint}`);
                console.log('📦 Test data:', testData);

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });

                const responseText = await response.text();
                let responseData = null;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = { raw: responseText };
                }

                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ ${endpoint} - Succès!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Headers:</strong></p>
                            <pre>${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}</pre>
                            <p><strong>Réponse:</strong></p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ ${endpoint} - Erreur</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Headers:</strong></p>
                            <pre>${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}</pre>
                            <p><strong>Réponse:</strong></p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>🚨 ${endpoint} - Erreur Réseau</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <p><strong>Stack:</strong></p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testFormSubmission() {
            const resultsDiv = document.getElementById('form-results');
            resultsDiv.innerHTML = '<p>⏳ Test de soumission complète...</p>';

            // Simuler la logique du formulaire principal
            const hostname = window.location.hostname;
            const isLocal = hostname.includes('localhost') || hostname.includes('127.0.0.1');
            
            const primaryEndpoint = !isLocal ? '/.netlify/functions/lead' : '/api/lead';
            const fallbackEndpoint = primaryEndpoint === '/api/lead' ? '/.netlify/functions/lead' : '/api/lead';
            
            console.log('🎯 Primary endpoint:', primaryEndpoint);
            console.log('🔄 Fallback endpoint:', fallbackEndpoint);

            const testData = {
                name: "Test User Complete",
                company: "Test Company Complete",
                email: "<EMAIL>",
                phone: "+33987654321",
                status: "reprise",
                context: "Test complet avec fallback",
                hp: "",
                ts: Date.now(),
                utm_source: "test",
                utm_campaign: "debug"
            };

            try {
                // Essayer l'endpoint principal
                let response = await fetch(primaryEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                if (!response.ok) {
                    console.log('⚠️ Primary endpoint failed, trying fallback...');
                    response = await fetch(fallbackEndpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });
                }

                const responseText = await response.text();
                let responseData = null;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = { raw: responseText };
                }

                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Test Complet - Succès!</h3>
                            <p><strong>Endpoint utilisé:</strong> ${response.url}</p>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Réponse:</strong></p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Test Complet - Échec</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Réponse:</strong></p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>🚨 Test Complet - Erreur</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
