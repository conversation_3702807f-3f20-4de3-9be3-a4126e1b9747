/**
 * Manages search engine indexing based on staging environment
 */
export function configureSEOIndexing(): void {
  const isStaging = import.meta.env.VITE_IS_STAGING === 'true';
  
  if (isStaging) {
    // Add noindex, nofollow meta tag for staging
    const existingRobotsTag = document.querySelector('meta[name="robots"]');
    
    if (!existingRobotsTag) {
      const robotsTag = document.createElement('meta');
      robotsTag.name = 'robots';
      robotsTag.content = 'noindex, nofollow';
      document.head.appendChild(robotsTag);
    } else {
      // Update existing robots tag
      existingRobotsTag.setAttribute('content', 'noindex, nofollow');
    }
    
    console.log('🚫 Staging environment: Search engine indexing disabled');
  } else {
    // Production: Remove any noindex tags that might exist
    const robotsTag = document.querySelector('meta[name="robots"]');
    if (robotsTag && robotsTag.getAttribute('content')?.includes('noindex')) {
      robotsTag.remove();
    }
    
    console.log('✅ Production environment: Search engine indexing enabled');
  }
}