#!/usr/bin/env node

// Script de vérification avant déploiement
const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification de la configuration de déploiement...\n');

// Vérifier les fichiers essentiels
const requiredFiles = [
  'index.html',
  'netlify.toml',
  '_redirects',
  'netlify/functions/lead.mjs'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - OK`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
    allFilesExist = false;
  }
});

// Vérifier le contenu de netlify.toml
console.log('\n📋 Vérification de netlify.toml:');
try {
  const netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
  
  if (netlifyConfig.includes('publish = "."')) {
    console.log('✅ Configuration publish correcte');
  } else {
    console.log('❌ Configuration publish incorrecte');
    allFilesExist = false;
  }
  
  if (netlifyConfig.includes('directory = "netlify/functions"')) {
    console.log('✅ Configuration functions correcte');
  } else {
    console.log('❌ Configuration functions incorrecte');
    allFilesExist = false;
  }
  
  if (netlifyConfig.includes('/api/lead')) {
    console.log('✅ Redirection API configurée');
  } else {
    console.log('❌ Redirection API manquante');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ Erreur lecture netlify.toml:', error.message);
  allFilesExist = false;
}

// Vérifier la fonction Netlify
console.log('\n🔧 Vérification de la fonction Netlify:');
try {
  const functionContent = fs.readFileSync('netlify/functions/lead.mjs', 'utf8');
  
  if (functionContent.includes('export default')) {
    console.log('✅ Fonction utilise la nouvelle API');
  } else {
    console.log('❌ Fonction utilise l\'ancienne API');
    allFilesExist = false;
  }
  
  if (functionContent.includes('Netlify.env.get')) {
    console.log('✅ Variables d\'environnement correctes');
  } else {
    console.log('❌ Variables d\'environnement incorrectes');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ Erreur lecture fonction:', error.message);
  allFilesExist = false;
}

// Vérifier le formulaire dans index.html
console.log('\n📝 Vérification du formulaire:');
try {
  const indexContent = fs.readFileSync('index.html', 'utf8');
  
  if (indexContent.includes('getApiEndpoint()')) {
    console.log('✅ Détection d\'environnement présente');
  } else {
    console.log('❌ Détection d\'environnement manquante');
    allFilesExist = false;
  }
  
  if (indexContent.includes('submitToApi(')) {
    console.log('✅ Fonction de fallback présente');
  } else {
    console.log('❌ Fonction de fallback manquante');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ Erreur lecture index.html:', error.message);
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 Tout est prêt pour le déploiement !');
  console.log('\n📋 Prochaines étapes:');
  console.log('1. git add .');
  console.log('2. git commit -m "Fix: Netlify deployment configuration"');
  console.log('3. git push origin main');
  console.log('4. Attendre le déploiement (2-5 min)');
  console.log('5. Tester sur staging.hai-systems.com');
  process.exit(0);
} else {
  console.log('❌ Des problèmes ont été détectés. Veuillez les corriger avant de déployer.');
  process.exit(1);
}
