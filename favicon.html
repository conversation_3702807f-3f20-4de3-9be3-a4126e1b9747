<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>HAI Systems</title>

    <!-- Google Fonts -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet"></noscript>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <style>
        /* Reset et base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            line-height: 1.6;
            color: var(--text-secondary);
            background: var(--bg-primary);
            overflow-x: hidden;
            transition: background-color var(--transition-theme), color var(--transition-theme);
            min-height: 100vh;
        }

        /* Variables CSS - Système de thèmes Dark/Light */
        :root {
            /* Accents néon rouge (identiques pour les deux thèmes) */
            --neon-red: #ff0040;
            --neon-red-dark: #cc0033;
            --neon-red-light: #ff3366;

            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-smooth: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-theme: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Thème clair (par défaut) */
        :root,
        [data-theme="light"] {
            /* Couleurs principales */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;

            /* Couleurs de texte */
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
        }

        /* Thème sombre */
        [data-theme="dark"] {
            /* Couleurs principales */
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;

            /* Couleurs de texte */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #b0bec5;
        }

        /* Full-screen logo container */
        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
            text-align: center;
        }

        /* Logo text styling */
        .logo-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .logo-hai {
            font-size: clamp(15rem, 40vw, 30rem);
            font-weight: 900;
            color: #ff0000;
            line-height: 0.8;
            letter-spacing: -0.03em;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .logo-container {
                padding: 1rem;
            }

            .logo-text {
                gap: 0.125rem;
            }

            .logo-hai {
                font-size: clamp(8rem, 35vw, 18rem);
            }
        }

        @media (max-width: 480px) {
            .logo-hai {
                font-size: clamp(6rem, 40vw, 15rem);
            }
        }
    </style>
</head>
<body>
    <div class="logo-container">
        <div class="logo-text">
            <div class="logo-hai">HAI</div>
        </div>
    </div>
</body>
</html>
