<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #0a0a0a;
            color: white;
            min-height: 200vh; /* Make page scrollable */
        }
        
        .test-card {
            width: 300px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            margin: 50px auto;
            padding: 20px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .test-card.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .glass-card {
            width: 300px;
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin: 50px auto;
            padding: 20px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .glass-card.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status" id="status">Loading...</div>
    
    <h1>Scroll Animation Test</h1>
    <p>Scroll down to test the animations</p>
    
    <div class="test-card">Test Card 1</div>
    <div class="glass-card">Glass Card 1</div>
    <div class="test-card">Test Card 2</div>
    <div class="glass-card">Glass Card 2</div>
    <div class="test-card">Test Card 3</div>
    <div class="glass-card">Glass Card 3</div>
    <div class="test-card">Test Card 4</div>
    <div class="glass-card">Glass Card 4</div>
    
    <script>
        // Copy the ScrollAnimationManager from the main file
        class ScrollAnimationManager {
            constructor() {
                this.animatedElements = new Set();
                this.pendingTimeouts = new Map();
                this.observer = null;
                this.init();
            }

            init() {
                this.observer = new IntersectionObserver(
                    (entries) => this.handleIntersection(entries),
                    {
                        root: null,
                        rootMargin: '0px 0px -15% 0px',
                        threshold: [0, 0.1, 0.25, 0.5, 0.75, 1.0]
                    }
                );
                this.observeElements();
                console.log('🎬 ScrollAnimationManager initialized');
            }

            observeElements() {
                const elements = document.querySelectorAll('.test-card, .glass-card');
                let observedCount = 0;
                elements.forEach(element => {
                    if (!element.classList.contains('animated')) {
                        this.observer.observe(element);
                        observedCount++;
                    }
                });
                console.log(`📊 Observing ${observedCount} elements`);
                document.getElementById('status').textContent = `Observing ${observedCount} elements`;
            }

            handleIntersection(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.intersectionRatio >= 0.15) {
                        console.log(`✨ Animating element:`, entry.target.textContent);
                        this.animateElement(entry.target);
                    }
                });
            }

            animateElement(element) {
                if (this.animatedElements.has(element)) {
                    return;
                }

                if (this.pendingTimeouts.has(element)) {
                    clearTimeout(this.pendingTimeouts.get(element));
                    this.pendingTimeouts.delete(element);
                }

                this.animatedElements.add(element);

                const allElements = Array.from(document.querySelectorAll('.test-card, .glass-card'));
                const elementIndex = allElements.indexOf(element);
                const delay = Math.min(elementIndex % 6, 5) * 60;

                const timeoutId = setTimeout(() => {
                    requestAnimationFrame(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                        element.classList.add('animated');
                        
                        this.observer.unobserve(element);
                        this.pendingTimeouts.delete(element);
                        
                        const animatedCount = this.animatedElements.size;
                        document.getElementById('status').textContent = `Animated ${animatedCount} elements`;
                    });
                }, delay);

                this.pendingTimeouts.set(element, timeoutId);
            }

            cleanup() {
                this.pendingTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
                this.pendingTimeouts.clear();
                if (this.observer) {
                    this.observer.disconnect();
                }
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if ('IntersectionObserver' in window) {
                const manager = new ScrollAnimationManager();
                window.addEventListener('beforeunload', () => manager.cleanup());
            } else {
                document.getElementById('status').textContent = 'IntersectionObserver not supported';
            }
        });
    </script>
</body>
</html>
